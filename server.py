import os
from dotenv import load_dotenv
from flask import Flask, render_template, send_from_directory
from flask_socketio import So<PERSON><PERSON>, emit
from langchain_openai import Chat<PERSON>penAI
from langchain.chains import Conversation<PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain.prompts import PromptTemplate
from datetime import datetime

load_dotenv()
openrouter_api_key = os.getenv("OPENROUTER_API_KEY")

app = Flask(__name__, static_folder="static", template_folder="templates")
socketio = SocketIO(app)

# === LLM Setup ===
llm = ChatOpenAI(
    model="deepseek/deepseek-chat-v3-0324:free",
    temperature=0.3,
    api_key=openrouter_api_key,
    base_url="https://openrouter.ai/api/v1",
)
memory = ConversationBufferMemory()

prompt = PromptTemplate(
    input_variables=["history", "input"],
    template="""
You are a legal assistant. Help the user draft legal documents through a step-by-step conversation.

When you're gathering information, ask concise questions.

✅ Once you have all the necessary details, generate the final document **only once**, and format it like this:

**DRAFTED DOCUMENT START**

# Title of the Document

**Parties:** ...
**Terms:** ...
...

**DRAFTED DOCUMENT END**

The document must be in **Markdown** (using `**bold**`, `# headings`, bullet points, etc).

Chat History:
{history}

User: {input}
Legal Assistant:""",
)

conversation = ConversationChain(llm=llm, memory=memory, prompt=prompt, verbose=False)


def is_document_generated(response: str) -> bool:
    return (
        "**DRAFTED DOCUMENT START**" in response
        and "**DRAFTED DOCUMENT END**" in response
    )


def extract_document_only(response: str) -> str:
    start = response.find("**DRAFTED DOCUMENT START**") + len(
        "**DRAFTED DOCUMENT START**"
    )
    end = response.find("**DRAFTED DOCUMENT END**")
    return response[start:end].strip()


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/docs/<filename>")
def serve_file(filename):
    return send_from_directory("docs", filename)


@socketio.on("user_message")
def handle_user_message(user_input):
    response = conversation.predict(input=user_input)

    filename = None
    if is_document_generated(response):
        doc_text = extract_document_only(response)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"legal_doc_{timestamp}.md"
        os.makedirs("docs", exist_ok=True)
        with open(os.path.join("docs", filename), "w", encoding="utf-8") as f:
            f.write(doc_text)

    emit("bot_response", {"response": response, "filename": filename})


if __name__ == "__main__":
    socketio.run(app, debug=True)
