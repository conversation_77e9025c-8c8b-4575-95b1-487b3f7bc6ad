<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LegalEase - AI Legal Document Assistant</title>
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: "#f0f9ff",
                100: "#e0f2fe",
                200: "#bae6fd",
                300: "#7dd3fc",
                400: "#38bdf8",
                500: "#0ea5e9",
                600: "#0284c7",
                700: "#0369a1",
                800: "#075985",
                900: "#0c4a6e",
              },
              secondary: {
                50: "#f5f3ff",
                100: "#ede9fe",
                200: "#ddd6fe",
                300: "#c4b5fd",
                400: "#a78bfa",
                500: "#8b5cf6",
                600: "#7c3aed",
                700: "#6d28d9",
                800: "#5b21b6",
                900: "#4c1d95",
              },
            },
          },
        },
      };
    </script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .message-enter {
        animation: fadeIn 0.3s ease-out forwards;
      }

      .document-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .typing-indicator span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #4b5563;
        margin-right: 3px;
      }

      .chat-container {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      @media (min-width: 1024px) {
        .chat-container {
          grid-template-columns: 1fr 1fr;
        }
      }

      .markdown-content h1 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 1.5rem 0 1rem;
        color: #1e293b;
        border-bottom: 1px solid #e2e8f0;
        padding-bottom: 0.5rem;
      }

      .markdown-content h2 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 1.25rem 0 0.75rem;
        color: #1e293b;
      }

      .markdown-content p {
        margin-bottom: 1rem;
        line-height: 1.6;
        color: #334155;
      }

      .markdown-content ul,
      .markdown-content ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
      }

      .markdown-content li {
        margin-bottom: 0.5rem;
      }

      .markdown-content pre {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
        overflow-x: auto;
      }

      .markdown-content code {
        font-family: monospace;
        background-color: #f8fafc;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
      }

      .markdown-content blockquote {
        border-left: 4px solid #e2e8f0;
        padding-left: 1rem;
        margin-left: 0;
        color: #64748b;
        font-style: italic;
      }

      .document-toolbar {
        position: sticky;
        top: 0;
        background: white;
        z-index: 10;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e2e8f0;
      }

      .document-content {
        padding-top: 1rem;
      }

      .legal-badge {
        background-color: #e0f2fe;
        color: #0369a1;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .document-actions {
        position: sticky;
        bottom: 0;
        background: white;
        z-index: 10;
        padding: 1rem 0;
        border-top: 1px solid #e2e8f0;
      }

      .suggested-prompt {
        transition: all 0.2s ease;
      }

      .suggested-prompt:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
    </style>
  </head>
  <body class="bg-gradient-to-br from-primary-50 to-secondary-50 min-h-screen">
    <!-- Header -->
    <header
      class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50"
    >
      <div class="max-w-7xl mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="bg-primary-600 p-2 rounded-lg shadow-md">
              <i class="fas fa-gavel text-white text-xl"></i>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">LegalEase</h1>
              <p class="text-sm text-gray-600">
                AI-Powered Legal Document Assistant
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div
              class="hidden md:flex items-center space-x-2 text-sm text-gray-500 bg-primary-50 px-3 py-1.5 rounded-full"
            >
              <i class="fas fa-shield-alt text-primary-600"></i>
              <span>End-to-End Encryption</span>
            </div>
            <button
              class="hidden md:flex items-center space-x-2 text-sm text-primary-600 bg-primary-50 hover:bg-primary-100 px-3 py-1.5 rounded-full transition-colors"
            >
              <i class="fas fa-history"></i>
              <span>History</span>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto p-4 lg:p-6">
      <div class="chat-container">
        <!-- Chat Panel -->
        <div
          class="chat-panel bg-white rounded-xl shadow-lg overflow-hidden flex flex-col"
        >
          <div class="bg-gradient-to-r from-primary-600 to-secondary-600 p-4">
            <div class="flex items-center justify-between">
              <h2 class="text-white font-semibold flex items-center">
                <i class="fas fa-comments mr-2"></i>
                Legal Consultation
              </h2>
              <button
                class="text-white bg-black bg-opacity-20 hover:bg-opacity-30 p-1.5 rounded-full transition-colors"
              >
                <i class="fas fa-ellipsis-h"></i>
              </button>
            </div>
            <p class="text-primary-100 text-sm mt-1">
              Describe your legal document needs in plain English
            </p>
          </div>

          <div
            id="chat-window"
            class="flex-1 p-4 h-96 lg:h-[calc(100vh-300px)] overflow-y-auto space-y-4 bg-gray-50"
          >
            <div class="text-center text-gray-500 py-8">
              <div
                class="inline-block bg-white p-4 rounded-full shadow-md mb-3"
              >
                <i class="fas fa-robot text-3xl text-primary-500"></i>
              </div>
              <p class="text-lg font-medium mb-2">
                Hello! I'm your AI legal assistant
              </p>
              <p class="text-sm max-w-md mx-auto">
                I can help draft contracts, agreements, notices and other legal
                documents. How can I assist you today?
              </p>

              <div
                class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto"
              >
                <button
                  class="suggested-prompt bg-white border border-gray-200 rounded-lg p-3 text-sm text-left hover:border-primary-300 hover:bg-primary-50"
                >
                  <div class="flex items-center">
                    <div
                      class="bg-primary-100 text-primary-600 p-1 rounded mr-2"
                    >
                      <i class="fas fa-file-contract text-xs"></i>
                    </div>
                    <span>Draft a rental agreement</span>
                  </div>
                </button>
                <button
                  class="suggested-prompt bg-white border border-gray-200 rounded-lg p-3 text-sm text-left hover:border-primary-300 hover:bg-primary-50"
                >
                  <div class="flex items-center">
                    <div
                      class="bg-primary-100 text-primary-600 p-1 rounded mr-2"
                    >
                      <i class="fas fa-handshake text-xs"></i>
                    </div>
                    <span>Create an NDA template</span>
                  </div>
                </button>
                <button
                  class="suggested-prompt bg-white border border-gray-200 rounded-lg p-3 text-sm text-left hover:border-primary-300 hover:bg-primary-50"
                >
                  <div class="flex items-center">
                    <div
                      class="bg-primary-100 text-primary-600 p-1 rounded mr-2"
                    >
                      <i class="fas fa-envelope text-xs"></i>
                    </div>
                    <span>Write a cease and desist letter</span>
                  </div>
                </button>
                <button
                  class="suggested-prompt bg-white border border-gray-200 rounded-lg p-3 text-sm text-left hover:border-primary-300 hover:bg-primary-50"
                >
                  <div class="flex items-center">
                    <div
                      class="bg-primary-100 text-primary-600 p-1 rounded mr-2"
                    >
                      <i class="fas fa-briefcase text-xs"></i>
                    </div>
                    <span>Generate employment contract</span>
                  </div>
                </button>
              </div>
            </div>
          </div>

          <div class="p-4 bg-white border-t border-gray-200">
            <form id="chat-form" class="flex gap-2">
              <div class="flex-1 relative">
                <input
                  id="user-input"
                  type="text"
                  placeholder="Describe the document you need (e.g. 'I need a rental agreement for my apartment')"
                  class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                  autocomplete="off"
                  required
                />
                <div
                  class="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-2"
                >
                  <button
                    type="button"
                    class="text-gray-400 hover:text-primary-500 transition-colors"
                  >
                    <i class="fas fa-paperclip"></i>
                  </button>
                  <button
                    type="button"
                    class="text-gray-400 hover:text-primary-500 transition-colors"
                  >
                    <i class="fas fa-microphone"></i>
                  </button>
                </div>
              </div>
              <button
                id="send-button"
                type="submit"
                class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2 shadow-md"
              >
                <span>Send</span>
                <i class="fas fa-paper-plane"></i>
              </button>
            </form>
            <p class="text-xs text-gray-500 mt-2 text-center">
              LegalEase provides general legal information, not professional
              advice
            </p>
          </div>
        </div>

        <!-- Document Preview Panel -->
        <div
          class="document-panel bg-white rounded-xl shadow-lg overflow-hidden flex flex-col"
        >
          <div class="bg-gradient-to-r from-green-600 to-emerald-600 p-4">
            <div class="flex items-center justify-between">
              <h2 class="text-white font-semibold flex items-center">
                <i class="fas fa-file-alt mr-2"></i>
                Document Preview
              </h2>
              <div class="flex space-x-2">
                <button
                  class="text-white bg-black bg-opacity-20 hover:bg-opacity-30 p-1.5 rounded-full transition-colors"
                >
                  <i class="fas fa-print"></i>
                </button>
                <button
                  class="text-white bg-black bg-opacity-20 hover:bg-opacity-30 p-1.5 rounded-full transition-colors"
                >
                  <i class="fas fa-ellipsis-h"></i>
                </button>
              </div>
            </div>
            <p class="text-green-100 text-sm mt-1">
              Review and download your generated document
            </p>
          </div>

          <div
            id="document-preview"
            class="flex-1 p-0 h-96 lg:h-[calc(100vh-300px)] overflow-y-auto bg-gray-50 relative"
          >
            <div id="empty-state" class="text-center text-gray-400 py-12 px-4">
              <div
                class="inline-block bg-white p-4 rounded-full shadow-md mb-3"
              >
                <i class="fas fa-file-text text-3xl text-green-500"></i>
              </div>
              <p class="text-lg font-medium mb-2">No Document Generated Yet</p>
              <p class="text-sm max-w-md mx-auto">
                Start a conversation to generate your legal document. Your
                document will appear here once created.
              </p>
            </div>

            <div id="document-content" class="hidden">
              <div class="document-toolbar px-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <span id="document-title" class="font-medium text-gray-700"
                      >Document</span
                    >
                    <span class="legal-badge">DRAFT</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <button
                      class="text-gray-500 hover:text-primary-600 transition-colors"
                      title="Zoom Out"
                    >
                      <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="text-sm text-gray-500">100%</span>
                    <button
                      class="text-gray-500 hover:text-primary-600 transition-colors"
                      title="Zoom In"
                    >
                      <i class="fas fa-search-plus"></i>
                    </button>
                  </div>
                </div>
              </div>

              <div class="document-content px-6 pb-6">
                <div id="markdown-content" class="markdown-content"></div>
              </div>
            </div>
          </div>

          <div id="document-actions" class="document-actions hidden">
            <div class="flex items-center justify-between mb-3 px-6">
              <div class="flex items-center text-sm text-gray-600">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                <span>Document ready for download</span>
              </div>
              <div class="text-xs text-gray-500" id="document-timestamp"></div>
            </div>
            <div class="flex gap-3 px-6">
              <button
                id="download-docx-btn"
                class="flex-1 bg-green-600 text-white px-4 py-2.5 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center space-x-2 shadow-sm"
              >
                <i class="fas fa-file-word"></i>
                <span>Download DOCX</span>
              </button>
              <button
                id="download-pdf-btn"
                class="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center justify-center space-x-2 shadow-sm"
              >
                <i class="fas fa-file-pdf"></i>
                <span>Download PDF</span>
              </button>
              <button
                id="copy-btn"
                class="bg-gray-600 text-white px-4 py-2.5 rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center justify-center shadow-sm"
                title="Copy to Clipboard"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script>
      const socket = io();
      const chatWindow = document.getElementById("chat-window");
      const form = document.getElementById("chat-form");
      const input = document.getElementById("user-input");
      const sendButton = document.getElementById("send-button");
      const documentPreview = document.getElementById("document-preview");
      const emptyState = document.getElementById("empty-state");
      const documentContent = document.getElementById("document-content");
      const markdownContent = document.getElementById("markdown-content");
      const documentActions = document.getElementById("document-actions");
      const downloadDocxBtn = document.getElementById("download-docx-btn");
      const downloadPdfBtn = document.getElementById("download-pdf-btn");
      const copyBtn = document.getElementById("copy-btn");
      const documentTimestamp = document.getElementById("document-timestamp");
      const documentTitle = document.getElementById("document-title");

      let currentDocumentContent = "";
      let currentFilename = "";
      let currentDocumentType = "";

      // Suggested prompt buttons
      const suggestedPrompts = document.querySelectorAll(".suggested-prompt");
      suggestedPrompts.forEach((button) => {
        button.addEventListener("click", (e) => {
          e.preventDefault();
          const text = button.querySelector("span").textContent;
          input.value = text;
          input.focus();
        });
      });

      function addMessage(role, text) {
        const msgDiv = document.createElement("div");

        if (role === "user") {
          msgDiv.className = "flex justify-end message-enter";
          msgDiv.innerHTML = `
            <div class="bg-primary-600 text-white p-3 rounded-lg max-w-xs lg:max-w-md shadow-sm">
              <div class="flex items-start space-x-2">
                <div class="flex-1 whitespace-pre-wrap">${escapeHtml(
                  text
                )}</div>
                <div class="flex-shrink-0 w-6 h-6 rounded-full bg-primary-700 flex items-center justify-center">
                  <i class="fas fa-user text-primary-200 text-xs"></i>
                </div>
              </div>
            </div>
          `;
        } else {
          msgDiv.className = "flex justify-start message-enter";
          msgDiv.innerHTML = `
            <div class="bg-white border border-gray-200 p-3 rounded-lg max-w-xs lg:max-w-md shadow-sm">
              <div class="flex items-start space-x-2">
                <div class="flex-shrink-0 w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center">
                  <i class="fas fa-robot text-primary-600 text-xs"></i>
                </div>
                <div class="flex-1 whitespace-pre-wrap text-gray-700">${escapeHtml(
                  text
                )}</div>
              </div>
            </div>
          `;
        }

        chatWindow.appendChild(msgDiv);
        chatWindow.scrollTop = chatWindow.scrollHeight;
      }

      function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }

      function showTypingIndicator() {
        const typingDiv = document.createElement("div");
        typingDiv.className = "flex justify-start typing-message";
        typingDiv.innerHTML = `
          <div class="bg-white border border-gray-200 p-3 rounded-lg shadow-sm">
            <div class="flex items-center space-x-2">
              <div class="flex-shrink-0 w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center">
                <i class="fas fa-robot text-primary-600 text-xs"></i>
              </div>
              <div class="typing-indicator flex items-center space-x-1">
                <span class="inline-block w-2 h-2 bg-gray-400 rounded-full animate-bounce"></span>
                <span class="inline-block w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></span>
                <span class="inline-block w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></span>
              </div>
              <span class="text-gray-500 text-sm">AI is drafting your document...</span>
            </div>
          </div>
        `;
        chatWindow.appendChild(typingDiv);
        chatWindow.scrollTop = chatWindow.scrollHeight;
      }

      function removeTypingIndicator() {
        const typingMessage = chatWindow.querySelector(".typing-message");
        if (typingMessage) {
          typingMessage.remove();
        }
      }

      function updateDocumentPreview(content, filename, documentType) {
        currentDocumentContent = content;
        currentFilename = filename;
        currentDocumentType = documentType || "Legal Document";

        // Hide empty state and show document content
        emptyState.classList.add("hidden");
        documentContent.classList.remove("hidden");

        // Update document title
        documentTitle.textContent = currentDocumentType;

        // Convert markdown to HTML
        const htmlContent = marked.parse(content);
        markdownContent.innerHTML = htmlContent;

        // Show document actions
        documentActions.classList.remove("hidden");

        // Update timestamp
        const now = new Date();
        documentTimestamp.textContent = `Generated: ${now.toLocaleString()}`;

        // Setup download buttons
        downloadDocxBtn.onclick = () => {
          const link = document.createElement("a");
          link.href = `/docs/${filename}.docx`;
          link.download = `${filename}.docx`;
          link.click();
        };

        downloadPdfBtn.onclick = () => {
          const link = document.createElement("a");
          link.href = `/docs/${filename}.pdf`;
          link.download = `${filename}.pdf`;
          link.click();
        };

        // Setup copy button
        copyBtn.onclick = async () => {
          try {
            await navigator.clipboard.writeText(content);
            const originalIcon = copyBtn.querySelector("i");
            originalIcon.className = "fas fa-check";
            setTimeout(() => {
              originalIcon.className = "fas fa-copy";
            }, 2000);
          } catch (err) {
            console.error("Failed to copy text: ", err);
          }
        };
      }

      form.addEventListener("submit", (e) => {
        e.preventDefault();
        const userText = input.value.trim();
        if (!userText) return;

        addMessage("user", userText);
        input.value = "";
        input.disabled = true;
        sendButton.disabled = true;
        sendButton.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> <span>Sending...</span>';

        showTypingIndicator();

        socket.emit("user_message", userText);
      });

      socket.on("bot_response", (data) => {
        removeTypingIndicator();
        addMessage("bot", data.response);

        if (data.filename) {
          // Extract document content from response
          const startMarker = "**DRAFTED DOCUMENT START**";
          const endMarker = "**DRAFTED DOCUMENT END**";
          const startIndex = data.response.indexOf(startMarker);
          const endIndex = data.response.indexOf(endMarker);

          if (startIndex !== -1 && endIndex !== -1) {
            const documentContent = data.response
              .substring(startIndex + startMarker.length, endIndex)
              .trim();

            // Try to extract document type from the first line
            let docType = "Legal Document";
            const firstLineEnd = documentContent.indexOf("\n");
            if (firstLineEnd !== -1) {
              const firstLine = documentContent
                .substring(0, firstLineEnd)
                .trim();
              if (firstLine) {
                docType = firstLine.replace(/^#+\s*/, ""); // Remove markdown headings
              }
            }

            updateDocumentPreview(documentContent, data.filename, docType);
          }
        }

        input.disabled = false;
        sendButton.disabled = false;
        sendButton.innerHTML =
          '<span>Send</span> <i class="fas fa-paper-plane"></i>';
        input.focus();
      });

      // Auto-focus input on page load
      window.addEventListener("load", () => {
        input.focus();
      });
    </script>
  </body>
</html>
