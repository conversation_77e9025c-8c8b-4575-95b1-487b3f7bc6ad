<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Legal Document Chatbot</title>
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-100 min-h-screen">
    <div class="max-w-4xl mx-auto p-4">
      <h1 class="text-3xl font-bold mb-4 text-center text-blue-700">Legal Assistant Chat</h1>

      <div id="chat-window" class="bg-white rounded-lg shadow p-4 h-[70vh] overflow-y-auto space-y-4">
        <!-- Chat messages will appear here -->
      </div>

      <form id="chat-form" class="mt-4 flex gap-2">
        <input
          id="user-input"
          type="text"
          placeholder="Type your message..."
          class="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring focus:border-blue-400"
          autocomplete="off"
          required
        />
        <button
          id="send-button"
          type="submit"
          class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          Send
        </button>
      </form>

      <div id="download-link" class="mt-4 hidden">
        <a
          id="doc-download"
          class="text-green-600 underline"
          href=""
          download
        >
          📥 Download Generated Document
        </a>
      </div>
    </div>

    <script>
      const socket = io();
      const chatWindow = document.getElementById("chat-window");
      const form = document.getElementById("chat-form");
      const input = document.getElementById("user-input");
      const sendButton = document.getElementById("send-button");
      const downloadSection = document.getElementById("download-link");
      const downloadLink = document.getElementById("doc-download");

      function addMessage(role, text) {
        const msgDiv = document.createElement("div");
        msgDiv.className = `p-3 rounded-md max-w-xl whitespace-pre-wrap ${
          role === "user"
            ? "bg-blue-100 self-end text-right"
            : "bg-gray-200 self-start"
        }`;
        msgDiv.textContent = text;
        chatWindow.appendChild(msgDiv);
        chatWindow.scrollTop = chatWindow.scrollHeight;
      }

      form.addEventListener("submit", (e) => {
        e.preventDefault();
        const userText = input.value.trim();
        if (!userText) return;

        addMessage("user", userText);
        input.value = "";
        input.disabled = true;
        sendButton.disabled = true;

        addMessage("bot", "🤖 AI is thinking...");

        socket.emit("user_message", userText);
      });

      socket.on("bot_response", (data) => {
        const botMessages = chatWindow.querySelectorAll(".bg-gray-200");
        const latest = botMessages[botMessages.length - 1];
        latest.textContent = data.response;

        if (data.filename) {
          downloadLink.href = `/docs/${data.filename}`;
          downloadSection.classList.remove("hidden");
        }

        input.disabled = false;
        sendButton.disabled = false;
        input.focus();
      });
    </script>
  </body>
</html>
