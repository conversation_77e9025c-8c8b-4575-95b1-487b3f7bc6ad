import os
import sys
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain.chains import Conversation<PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain.prompts import PromptTemplate
from markdown import markdown
from datetime import datetime
# import pdfkit

# === Load Environment ===
load_dotenv()
openrouter_api_key = os.getenv("OPENROUTER_API_KEY")

# === Setup LLM ===
llm = ChatOpenAI(
    model="deepseek/deepseek-chat-v3-0324:free",
    temperature=0.3,
    api_key=openrouter_api_key,
    base_url="https://openrouter.ai/api/v1",
)

memory = ConversationBufferMemory()

# === Prompt Template ===
legal_prompt = PromptTemplate(
    input_variables=["history", "input"],
    template="""
You are a legal assistant. Help the user draft legal documents through a step-by-step conversation.

When you're gathering information, ask concise questions.

✅ Once you have all the necessary details, generate the final document **only once**, and format it like this:

**DRAFTED DOCUMENT START**

# Title of the Document

**Parties:** ...
**Terms:** ...
...

**DRAFTED DOCUMENT END**

The document must be in **Markdown** (using `**bold**`, `# headings`, bullet points, etc).

Chat History:
{history}

User: {input}
Legal Assistant:""",
)

conversation = ConversationChain(
    llm=llm, memory=memory, prompt=legal_prompt, verbose=False
)

# === Document Detection ===
def is_document_generated(response: str) -> bool:
    return (
        "**DRAFTED DOCUMENT START**" in response
        and "**DRAFTED DOCUMENT END**" in response
    )

# === Extract Only the Final Document ===
def extract_document_only(response: str) -> str:
    start = response.find("**DRAFTED DOCUMENT START**") + len("**DRAFTED DOCUMENT START**")
    end = response.find("**DRAFTED DOCUMENT END**")
    return response[start:end].strip()

# === Save as PDF ===
# def save_pdf_from_markdown(md_text: str, filename: str):
#     html_content = markdown(md_text)
#     pdfkit.from_string(html_content, filename)

# === Main Chat Function ===
def chat(output_dir: str = "docs"):
    print("🤖 Legal Document ChatBot (type 'exit' to quit)\n")
    os.makedirs(output_dir, exist_ok=True)

    while True:
        user_input = input("You: ")
        if user_input.strip().lower() in ["exit", "quit"]:
            print("👋 Exiting. Goodbye!")
            break

        response = conversation.predict(input=user_input)
        print(f"Bot: {response}\n")

        if is_document_generated(response):
            doc_text = extract_document_only(response)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"legal_doc_{timestamp}"
            txt_path = os.path.join(output_dir, f"{base_filename}.md")
            pdf_path = os.path.join(output_dir, f"{base_filename}.pdf")

            with open(txt_path, "w", encoding="utf-8") as f:
                f.write(doc_text)

            # save_pdf_from_markdown(doc_text, pdf_path)

            print(f"✅ Document auto-saved as:\n📄 {txt_path}\n📄 {pdf_path}\n")


# === CLI Entrypoint ===
if __name__ == "__main__":
    output_dir = "docs"
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    chat(output_dir)
