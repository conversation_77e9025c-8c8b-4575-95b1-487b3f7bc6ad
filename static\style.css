/* Custom styles for Legal Assistant */

.chat-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  height: calc(100vh - 200px);
}

@media (max-width: 1024px) {
  .chat-container {
    grid-template-columns: 1fr;
    height: auto;
  }
  .document-panel {
    order: 2;
  }
  .chat-panel {
    order: 1;
  }
}

/* Markdown content styling */
.markdown-content h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.markdown-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #374151;
}

.markdown-content h3 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #4b5563;
}

.markdown-content p {
  margin-bottom: 0.75rem;
  color: #374151;
  line-height: 1.6;
}

.markdown-content ul {
  list-style-type: disc;
  list-style-position: inside;
  margin-bottom: 0.75rem;
  color: #374151;
}

.markdown-content ol {
  list-style-type: decimal;
  list-style-position: inside;
  margin-bottom: 0.75rem;
  color: #374151;
}

.markdown-content li {
  margin-bottom: 0.25rem;
  padding-left: 0.5rem;
}

.markdown-content strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  font-style: italic;
  color: #4b5563;
  margin-bottom: 0.75rem;
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 0.375rem;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.markdown-content th {
  background-color: #f3f4f6;
  font-weight: 600;
}

.markdown-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.markdown-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

/* Typing indicator animation */
.typing-indicator {
  display: inline-block;
}

.typing-indicator span {
  animation: bounce 1.4s ease-in-out infinite both;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Button hover effects */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #10b981, #059669);
  transition: all 0.3s ease;
}

.btn-success:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Card animations */
.card-animate {
  transition: all 0.3s ease;
}

.card-animate:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Message animations */
.message-enter {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Document preview fade-in */
.document-fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Status indicators */
.status-success {
  color: #10b981;
}

.status-warning {
  color: #f59e0b;
}

.status-error {
  color: #ef4444;
}

/* Responsive text sizes */
@media (max-width: 640px) {
  .markdown-content h1 {
    font-size: 1.25rem;
  }
  
  .markdown-content h2 {
    font-size: 1.125rem;
  }
  
  .markdown-content h3 {
    font-size: 1rem;
  }
}
